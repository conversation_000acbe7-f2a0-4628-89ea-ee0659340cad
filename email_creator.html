<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Creator & Manager</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .main-content {
            display: flex;
            min-height: 600px;
        }
        
        .sidebar {
            width: 300px;
            background: #f8f9fa;
            border-right: 1px solid #e0e0e0;
            padding: 2rem;
        }
        
        .content-area {
            flex: 1;
            padding: 2rem;
        }
        
        .tab-buttons {
            display: flex;
            margin-bottom: 2rem;
            background: #f0f0f0;
            border-radius: 10px;
            padding: 0.5rem;
        }
        
        .tab-button {
            flex: 1;
            padding: 1rem;
            background: transparent;
            border: none;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .tab-button.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 600;
        }
        
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .form-group textarea {
            min-height: 120px;
            resize: vertical;
        }
        
        .btn {
            padding: 1rem 2rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
            font-weight: 600;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .email-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        
        .email-item {
            padding: 1rem;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .email-item:hover {
            background: #f8f9fa;
        }
        
        .email-item:last-child {
            border-bottom: none;
        }
        
        .email-item.selected {
            background: #e3f2fd;
            border-left: 4px solid #667eea;
        }
        
        .email-preview {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 1.5rem;
            margin-top: 1rem;
        }
        
        .email-header {
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 1rem;
            margin-bottom: 1rem;
        }
        
        .email-header h3 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .email-meta {
            color: #666;
            font-size: 0.9rem;
        }
        
        .email-body {
            line-height: 1.6;
            color: #333;
        }
        
        .generator-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .option-card {
            background: #f8f9fa;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .option-card:hover {
            border-color: #667eea;
            background: #f0f4ff;
        }
        
        .option-card.selected {
            border-color: #667eea;
            background: #e3f2fd;
        }
        
        .option-card h4 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .option-card p {
            color: #666;
            font-size: 0.9rem;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 8px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .message {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            display: none;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .message.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📧 Email Creator & Manager</h1>
            <p>Create, manage, and organize your email templates and accounts</p>
        </div>
        
        <div class="main-content">
            <div class="sidebar">
                <h3 style="margin-bottom: 1rem; color: #333;">📊 Statistics</h3>
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number" id="total-emails">0</div>
                        <div class="stat-label">Total Emails</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="total-templates">0</div>
                        <div class="stat-label">Templates</div>
                    </div>
                </div>
                
                <h3 style="margin-bottom: 1rem; color: #333;">📋 Email List</h3>
                <div class="email-list" id="email-list">
                    <div style="padding: 2rem; text-align: center; color: #666;">
                        No emails created yet
                    </div>
                </div>
                
                <button class="btn" style="width: 100%; margin-top: 1rem;" onclick="clearAllEmails()">
                    🗑️ Clear All
                </button>
            </div>
            
            <div class="content-area">
                <div class="tab-buttons">
                    <button class="tab-button active" onclick="showTab('create')">✉️ Create Email</button>
                    <button class="tab-button" onclick="showTab('generate')">🎲 Generate</button>
                    <button class="tab-button" onclick="showTab('templates')">📄 Templates</button>
                    <button class="tab-button" onclick="showTab('preview')">👁️ Preview</button>
                </div>
                
                <div id="message" class="message"></div>
                
                <!-- Create Email Tab -->
                <div id="create-tab" class="tab-content active">
                    <h2 style="margin-bottom: 1.5rem; color: #333;">Create New Email</h2>
                    <form onsubmit="createEmail(event)">
                        <div class="form-group">
                            <label for="email-to">To (Email Address)</label>
                            <input type="email" id="email-to" required placeholder="<EMAIL>">
                        </div>
                        
                        <div class="form-group">
                            <label for="email-from">From (Your Email)</label>
                            <input type="email" id="email-from" required placeholder="<EMAIL>">
                        </div>
                        
                        <div class="form-group">
                            <label for="email-subject">Subject</label>
                            <input type="text" id="email-subject" required placeholder="Enter email subject">
                        </div>
                        
                        <div class="form-group">
                            <label for="email-body">Message Body</label>
                            <textarea id="email-body" required placeholder="Write your email message here..."></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="email-priority">Priority</label>
                            <select id="email-priority">
                                <option value="normal">Normal</option>
                                <option value="high">High</option>
                                <option value="low">Low</option>
                            </select>
                        </div>
                        
                        <button type="submit" class="btn">📧 Create Email</button>
                        <button type="button" class="btn btn-secondary" onclick="saveAsTemplate()" style="margin-left: 1rem;">💾 Save as Template</button>
                    </form>
                </div>
                
                <!-- Generate Email Tab -->
                <div id="generate-tab" class="tab-content">
                    <h2 style="margin-bottom: 1.5rem; color: #333;">Generate Email</h2>
                    
                    <div class="generator-options">
                        <div class="option-card" onclick="selectEmailType('business')">
                            <h4>💼 Business</h4>
                            <p>Professional business emails</p>
                        </div>
                        <div class="option-card" onclick="selectEmailType('personal')">
                            <h4>👤 Personal</h4>
                            <p>Casual personal messages</p>
                        </div>
                        <div class="option-card" onclick="selectEmailType('marketing')">
                            <h4>📢 Marketing</h4>
                            <p>Promotional campaigns</p>
                        </div>
                        <div class="option-card" onclick="selectEmailType('support')">
                            <h4>🛠️ Support</h4>
                            <p>Customer support emails</p>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="generate-topic">Topic/Subject</label>
                        <input type="text" id="generate-topic" placeholder="What is this email about?">
                    </div>
                    
                    <button class="btn" onclick="generateEmail()">🎲 Generate Email</button>
                </div>
                
                <!-- Templates Tab -->
                <div id="templates-tab" class="tab-content">
                    <h2 style="margin-bottom: 1.5rem; color: #333;">Email Templates</h2>
                    <div id="templates-list">
                        <div style="padding: 2rem; text-align: center; color: #666;">
                            No templates saved yet
                        </div>
                    </div>
                </div>
                
                <!-- Preview Tab -->
                <div id="preview-tab" class="tab-content">
                    <h2 style="margin-bottom: 1.5rem; color: #333;">Email Preview</h2>
                    <div id="email-preview">
                        <div style="padding: 2rem; text-align: center; color: #666;">
                            Select an email from the sidebar to preview
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
