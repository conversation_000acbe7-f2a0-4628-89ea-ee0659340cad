<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Creator & Manager</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
            position: relative;
            z-index: 1;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            z-index: -1;
            opacity: 0.9;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .main-content {
            display: flex;
            min-height: 600px;
        }
        
        .sidebar {
            width: 300px;
            background: #f8f9fa;
            border-right: 1px solid #e0e0e0;
            padding: 2rem;
        }
        
        .content-area {
            flex: 1;
            padding: 2rem;
        }
        
        .tab-buttons {
            display: flex;
            margin-bottom: 2rem;
            background: #f0f0f0;
            border-radius: 10px;
            padding: 0.5rem;
        }
        
        .tab-button {
            flex: 1;
            padding: 1rem;
            background: transparent;
            border: none;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .tab-button.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 600;
        }
        
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .form-group textarea {
            min-height: 120px;
            resize: vertical;
        }
        
        .btn {
            padding: 1rem 2rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
            font-weight: 600;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .email-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        
        .email-item {
            padding: 1rem;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .email-item:hover {
            background: #f8f9fa;
        }
        
        .email-item:last-child {
            border-bottom: none;
        }
        
        .email-item.selected {
            background: #e3f2fd;
            border-left: 4px solid #667eea;
        }
        
        .email-preview {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 1.5rem;
            margin-top: 1rem;
        }
        
        .email-header {
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 1rem;
            margin-bottom: 1rem;
        }
        
        .email-header h3 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .email-meta {
            color: #666;
            font-size: 0.9rem;
        }
        
        .email-body {
            line-height: 1.6;
            color: #333;
        }
        
        .generator-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .option-card {
            background: #f8f9fa;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .option-card:hover {
            border-color: #667eea;
            background: #f0f4ff;
        }
        
        .option-card.selected {
            border-color: #667eea;
            background: #e3f2fd;
        }
        
        .option-card h4 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .option-card p {
            color: #666;
            font-size: 0.9rem;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 8px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .message {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            display: none;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .message.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        /* Multi-User Messaging Styles */
        .recipient-tag {
            background: #667eea;
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.9rem;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .recipient-tag .remove-tag {
            background: rgba(255, 255, 255, 0.3);
            border: none;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .recipient-tag .remove-tag:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        .sent-message-item {
            padding: 1rem;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: background 0.3s;
        }

        .sent-message-item:hover {
            background: #f8f9fa;
        }

        .sent-message-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .sent-message-subject {
            font-weight: 600;
            color: #333;
        }

        .sent-message-time {
            font-size: 0.8rem;
            color: #666;
        }

        .sent-message-recipients {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 0.5rem;
        }

        .sent-message-preview {
            font-size: 0.9rem;
            color: #888;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .message-type-badge {
            padding: 0.2rem 0.6rem;
            border-radius: 10px;
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .message-type-normal {
            background: #e9ecef;
            color: #495057;
        }

        .message-type-urgent {
            background: #f8d7da;
            color: #721c24;
        }

        .message-type-announcement {
            background: #d1ecf1;
            color: #0c5460;
        }

        .message-type-reminder {
            background: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📧 Email Creator & Manager</h1>
            <p>Create, manage, and organize your email templates and accounts</p>
        </div>
        
        <div class="main-content">
            <div class="sidebar">
                <h3 style="margin-bottom: 1rem; color: #333;">📊 Statistics</h3>
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number" id="total-emails">0</div>
                        <div class="stat-label">Total Emails</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="total-templates">0</div>
                        <div class="stat-label">Templates</div>
                    </div>
                </div>
                
                <h3 style="margin-bottom: 1rem; color: #333;">📋 Email List</h3>
                <div class="email-list" id="email-list">
                    <div style="padding: 2rem; text-align: center; color: #666;">
                        No emails created yet
                    </div>
                </div>
                
                <button class="btn" style="width: 100%; margin-top: 1rem;" onclick="clearAllEmails()">
                    🗑️ Clear All
                </button>
            </div>
            
            <div class="content-area">
                <div class="tab-buttons">
                    <button class="tab-button active" onclick="showTab('create')">✉️ Create Email</button>
                    <button class="tab-button" onclick="showTab('generate')">🎲 Generate</button>
                    <button class="tab-button" onclick="showTab('templates')">📄 Templates</button>
                    <button class="tab-button" onclick="showTab('messaging')">💬 Multi-User</button>
                    <button class="tab-button" onclick="showTab('preview')">👁️ Preview</button>
                </div>
                
                <div id="message" class="message"></div>
                
                <!-- Create Email Tab -->
                <div id="create-tab" class="tab-content active">
                    <h2 style="margin-bottom: 1.5rem; color: #333;">Create New Email</h2>
                    <form onsubmit="createEmail(event)">
                        <div class="form-group">
                            <label for="email-to">To (Email Address)</label>
                            <input type="email" id="email-to" required placeholder="<EMAIL>">
                        </div>
                        
                        <div class="form-group">
                            <label for="email-from">From (Your Email)</label>
                            <input type="email" id="email-from" required placeholder="<EMAIL>">
                        </div>
                        
                        <div class="form-group">
                            <label for="email-subject">Subject</label>
                            <input type="text" id="email-subject" required placeholder="Enter email subject">
                        </div>
                        
                        <div class="form-group">
                            <label for="email-body">Message Body</label>
                            <textarea id="email-body" required placeholder="Write your email message here..."></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="email-priority">Priority</label>
                            <select id="email-priority">
                                <option value="normal">Normal</option>
                                <option value="high">High</option>
                                <option value="low">Low</option>
                            </select>
                        </div>
                        
                        <button type="submit" class="btn">📧 Create Email</button>
                        <button type="button" class="btn btn-secondary" onclick="saveAsTemplate()" style="margin-left: 1rem;">💾 Save as Template</button>
                    </form>
                </div>
                
                <!-- Generate Email Tab -->
                <div id="generate-tab" class="tab-content">
                    <h2 style="margin-bottom: 1.5rem; color: #333;">Generate Email</h2>
                    
                    <div class="generator-options">
                        <div class="option-card" onclick="selectEmailType('business')">
                            <h4>💼 Business</h4>
                            <p>Professional business emails</p>
                        </div>
                        <div class="option-card" onclick="selectEmailType('personal')">
                            <h4>👤 Personal</h4>
                            <p>Casual personal messages</p>
                        </div>
                        <div class="option-card" onclick="selectEmailType('marketing')">
                            <h4>📢 Marketing</h4>
                            <p>Promotional campaigns</p>
                        </div>
                        <div class="option-card" onclick="selectEmailType('support')">
                            <h4>🛠️ Support</h4>
                            <p>Customer support emails</p>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="generate-topic">Topic/Subject</label>
                        <input type="text" id="generate-topic" placeholder="What is this email about?">
                    </div>
                    
                    <button class="btn" onclick="generateEmail()">🎲 Generate Email</button>
                </div>
                
                <!-- Templates Tab -->
                <div id="templates-tab" class="tab-content">
                    <h2 style="margin-bottom: 1.5rem; color: #333;">Email Templates</h2>
                    <div id="templates-list">
                        <div style="padding: 2rem; text-align: center; color: #666;">
                            No templates saved yet
                        </div>
                    </div>
                </div>

                <!-- Multi-User Messaging Tab -->
                <div id="messaging-tab" class="tab-content">
                    <h2 style="margin-bottom: 1.5rem; color: #333;">Multi-User Email Messaging</h2>

                    <!-- User Selection -->
                    <div class="form-group">
                        <label for="sender-select">Send From (User Account)</label>
                        <select id="sender-select" onchange="updateSenderInfo()">
                            <option value="">Select sender...</option>
                            <option value="<EMAIL>">John Doe (<EMAIL>)</option>
                            <option value="<EMAIL>">Sarah Smith (<EMAIL>)</option>
                            <option value="<EMAIL>">Mike Johnson (<EMAIL>)</option>
                            <option value="custom">+ Add Custom User</option>
                        </select>
                    </div>

                    <!-- Custom User Input -->
                    <div id="custom-sender" class="form-group" style="display: none;">
                        <label for="custom-sender-email">Custom Sender Email</label>
                        <input type="email" id="custom-sender-email" placeholder="Enter custom sender email">
                    </div>

                    <!-- Recipients -->
                    <div class="form-group">
                        <label for="recipients">Send To (Recipients)</label>
                        <div id="recipient-tags" style="display: flex; flex-wrap: wrap; gap: 0.5rem; margin-bottom: 0.5rem;"></div>
                        <input type="email" id="recipient-input" placeholder="Enter recipient email and press Enter">
                        <small style="color: #666;">Press Enter to add multiple recipients</small>
                    </div>

                    <!-- Message Composition -->
                    <div class="form-group">
                        <label for="multi-subject">Subject</label>
                        <input type="text" id="multi-subject" placeholder="Enter email subject">
                    </div>

                    <div class="form-group">
                        <label for="multi-message">Message</label>
                        <textarea id="multi-message" placeholder="Write your message here..." style="min-height: 150px;"></textarea>
                    </div>

                    <!-- Message Type -->
                    <div class="form-group">
                        <label for="message-type">Message Type</label>
                        <select id="message-type">
                            <option value="normal">Normal</option>
                            <option value="urgent">Urgent</option>
                            <option value="announcement">Announcement</option>
                            <option value="reminder">Reminder</option>
                        </select>
                    </div>

                    <!-- Send Options -->
                    <div style="display: flex; gap: 1rem; margin-top: 1.5rem;">
                        <button class="btn" onclick="sendMultiUserEmail()">📧 Send Email</button>
                        <button class="btn btn-secondary" onclick="scheduleEmail()">⏰ Schedule Send</button>
                        <button class="btn btn-secondary" onclick="saveDraft()">💾 Save Draft</button>
                    </div>

                    <!-- Sent Messages History -->
                    <div style="margin-top: 2rem;">
                        <h3 style="color: #333; margin-bottom: 1rem;">📤 Sent Messages</h3>
                        <div id="sent-messages" class="email-list">
                            <div style="padding: 2rem; text-align: center; color: #666;">
                                No messages sent yet
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Preview Tab -->
                <div id="preview-tab" class="tab-content">
                    <h2 style="margin-bottom: 1.5rem; color: #333;">Email Preview</h2>
                    <div id="email-preview">
                        <div style="padding: 2rem; text-align: center; color: #666;">
                            Select an email from the sidebar to preview
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let emails = JSON.parse(localStorage.getItem('emails')) || [];
        let templates = JSON.parse(localStorage.getItem('templates')) || [];
        let sentMessages = JSON.parse(localStorage.getItem('sentMessages')) || [];
        let recipients = [];

        // Tab switching functionality
        function showTab(tabName) {
            // Hide all tabs
            const tabs = document.querySelectorAll('.tab-content');
            tabs.forEach(tab => tab.classList.remove('active'));

            // Hide all tab buttons
            const buttons = document.querySelectorAll('.tab-button');
            buttons.forEach(btn => btn.classList.remove('active'));

            // Show selected tab
            document.getElementById(tabName + '-tab').classList.add('active');
            event.target.classList.add('active');

            // Update displays
            if (tabName === 'messaging') {
                displaySentMessages();
            }
        }

        // Multi-User Messaging Functions
        function updateSenderInfo() {
            const senderSelect = document.getElementById('sender-select');
            const customSender = document.getElementById('custom-sender');

            if (senderSelect.value === 'custom') {
                customSender.style.display = 'block';
            } else {
                customSender.style.display = 'none';
            }
        }

        // Recipient management
        document.getElementById('recipient-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                addRecipient();
            }
        });

        function addRecipient() {
            const input = document.getElementById('recipient-input');
            const email = input.value.trim();

            if (email && isValidEmail(email) && !recipients.includes(email)) {
                recipients.push(email);
                input.value = '';
                updateRecipientTags();
            }
        }

        function removeRecipient(email) {
            recipients = recipients.filter(r => r !== email);
            updateRecipientTags();
        }

        function updateRecipientTags() {
            const container = document.getElementById('recipient-tags');
            container.innerHTML = '';

            recipients.forEach(email => {
                const tag = document.createElement('div');
                tag.className = 'recipient-tag';
                tag.innerHTML = `
                    ${email}
                    <button class="remove-tag" onclick="removeRecipient('${email}')">×</button>
                `;
                container.appendChild(tag);
            });
        }

        function isValidEmail(email) {
            return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
        }

        // Send multi-user email
        function sendMultiUserEmail() {
            const senderSelect = document.getElementById('sender-select');
            const customSenderEmail = document.getElementById('custom-sender-email');
            const subject = document.getElementById('multi-subject').value;
            const message = document.getElementById('multi-message').value;
            const messageType = document.getElementById('message-type').value;

            // Get sender email
            let senderEmail = senderSelect.value;
            if (senderEmail === 'custom') {
                senderEmail = customSenderEmail.value;
            }

            // Validation
            if (!senderEmail || !isValidEmail(senderEmail)) {
                showMessage('Please select or enter a valid sender email', 'error');
                return;
            }

            if (recipients.length === 0) {
                showMessage('Please add at least one recipient', 'error');
                return;
            }

            if (!subject.trim()) {
                showMessage('Please enter a subject', 'error');
                return;
            }

            if (!message.trim()) {
                showMessage('Please enter a message', 'error');
                return;
            }

            // Create email object
            const emailData = {
                id: Date.now(),
                from: senderEmail,
                to: [...recipients],
                subject: subject,
                body: message,
                type: messageType,
                timestamp: new Date().toISOString(),
                status: 'sent'
            };

            // Save to sent messages
            sentMessages.unshift(emailData);
            localStorage.setItem('sentMessages', JSON.stringify(sentMessages));

            // Also add to main emails list
            emails.unshift(emailData);
            localStorage.setItem('emails', JSON.stringify(emails));

            // Clear form
            clearMessagingForm();

            // Show success message
            showMessage(`Email sent successfully to ${recipients.length} recipient(s)!`, 'success');

            // Update displays
            displaySentMessages();
            updateStats();
        }

        function clearMessagingForm() {
            document.getElementById('sender-select').value = '';
            document.getElementById('custom-sender-email').value = '';
            document.getElementById('multi-subject').value = '';
            document.getElementById('multi-message').value = '';
            document.getElementById('message-type').value = 'normal';
            recipients = [];
            updateRecipientTags();
            updateSenderInfo();
        }

        function displaySentMessages() {
            const container = document.getElementById('sent-messages');

            if (sentMessages.length === 0) {
                container.innerHTML = '<div style="padding: 2rem; text-align: center; color: #666;">No messages sent yet</div>';
                return;
            }

            container.innerHTML = sentMessages.map(msg => `
                <div class="sent-message-item" onclick="viewSentMessage(${msg.id})">
                    <div class="sent-message-header">
                        <div class="sent-message-subject">${msg.subject}</div>
                        <div style="display: flex; align-items: center; gap: 0.5rem;">
                            <span class="message-type-badge message-type-${msg.type}">${msg.type}</span>
                            <div class="sent-message-time">${formatTime(msg.timestamp)}</div>
                        </div>
                    </div>
                    <div class="sent-message-recipients">
                        To: ${msg.to.join(', ')}
                    </div>
                    <div class="sent-message-preview">
                        ${msg.body.substring(0, 100)}${msg.body.length > 100 ? '...' : ''}
                    </div>
                </div>
            `).join('');
        }

        function viewSentMessage(messageId) {
            const message = sentMessages.find(msg => msg.id === messageId);
            if (message) {
                // Switch to preview tab and show the message
                showTab('preview');
                displayEmailPreview(message);
            }
        }

        function displayEmailPreview(email) {
            const preview = document.getElementById('email-preview');
            preview.innerHTML = `
                <div class="email-preview">
                    <div class="email-header">
                        <h3>${email.subject}</h3>
                        <div class="email-meta">
                            <strong>From:</strong> ${email.from}<br>
                            <strong>To:</strong> ${email.to.join(', ')}<br>
                            <strong>Type:</strong> <span class="message-type-badge message-type-${email.type}">${email.type}</span><br>
                            <strong>Sent:</strong> ${formatTime(email.timestamp)}
                        </div>
                    </div>
                    <div class="email-body">
                        ${email.body.replace(/\n/g, '<br>')}
                    </div>
                </div>
            `;
        }

        function scheduleEmail() {
            showMessage('Email scheduling feature coming soon!', 'info');
        }

        function saveDraft() {
            showMessage('Draft saved successfully!', 'success');
        }

        function formatTime(timestamp) {
            return new Date(timestamp).toLocaleString();
        }

        function showMessage(text, type) {
            const messageDiv = document.getElementById('message');
            messageDiv.textContent = text;
            messageDiv.className = `message ${type}`;
            messageDiv.style.display = 'block';

            setTimeout(() => {
                messageDiv.style.display = 'none';
            }, 5000);
        }

        function updateStats() {
            document.getElementById('total-emails').textContent = emails.length;
            document.getElementById('total-templates').textContent = templates.length;
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateStats();
            displaySentMessages();
        });
    </script>
</body>
</html>
