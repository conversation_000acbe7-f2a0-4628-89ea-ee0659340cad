<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 2rem;
        }
        
        .container {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            width: 100%;
            max-width: 400px;
        }
        
        .tab-buttons {
            display: flex;
            margin-bottom: 2rem;
        }
        
        .tab-button {
            flex: 1;
            padding: 1rem;
            background: #f8f9fa;
            border: none;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .tab-button.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .tab-button:first-child {
            border-radius: 5px 0 0 5px;
        }
        
        .tab-button:last-child {
            border-radius: 0 5px 5px 0;
        }
        
        .form-container {
            display: none;
        }
        
        .form-container.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 500;
        }
        
        .form-group input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            width: 100%;
            padding: 0.75rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .message {
            padding: 0.75rem;
            border-radius: 5px;
            margin-bottom: 1rem;
            display: none;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .dashboard {
            display: none;
            text-align: center;
        }

        .dashboard.active {
            display: block;
        }

        .user-info {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 1rem 0;
        }

        /* WhatsApp-like Chat Interface */
        .chat-container {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #f0f0f0;
            z-index: 1000;
        }

        .chat-container.active {
            display: flex;
        }

        .chat-sidebar {
            width: 30%;
            min-width: 300px;
            background: white;
            border-right: 1px solid #e0e0e0;
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            background: #075e54;
            color: white;
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chat-header h3 {
            margin: 0;
        }

        .back-btn {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            font-size: 1.2rem;
            padding: 0.5rem;
            border-radius: 50%;
            transition: background 0.3s;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .online-users {
            flex: 1;
            overflow-y: auto;
            padding: 1rem 0;
        }

        .user-item {
            padding: 1rem;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: background 0.3s;
            display: flex;
            align-items: center;
        }

        .user-item:hover {
            background: #f5f5f5;
        }

        .user-item.active {
            background: #e3f2fd;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #075e54;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-weight: bold;
        }

        .user-details h4 {
            margin: 0;
            color: #333;
        }

        .user-details p {
            margin: 0;
            color: #666;
            font-size: 0.9rem;
        }

        .chat-main {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #e5ddd5;
        }

        .chat-main-header {
            background: #075e54;
            color: white;
            padding: 1rem;
            display: flex;
            align-items: center;
        }

        .chat-main-header .user-avatar {
            margin-right: 1rem;
        }

        .chat-messages {
            flex: 1;
            padding: 1rem;
            overflow-y: auto;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="chat-bg" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="2" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23chat-bg)"/></svg>');
        }

        .message {
            margin-bottom: 1rem;
            display: flex;
            align-items: flex-end;
        }

        .message.own {
            justify-content: flex-end;
        }

        .message-bubble {
            max-width: 70%;
            padding: 0.75rem 1rem;
            border-radius: 18px;
            position: relative;
            word-wrap: break-word;
        }

        .message.own .message-bubble {
            background: #dcf8c6;
            border-bottom-right-radius: 5px;
        }

        .message:not(.own) .message-bubble {
            background: white;
            border-bottom-left-radius: 5px;
        }

        .message-info {
            font-size: 0.75rem;
            color: #666;
            margin-top: 0.25rem;
            text-align: right;
        }

        .message:not(.own) .message-info {
            text-align: left;
        }

        .chat-input-container {
            background: #f0f0f0;
            padding: 1rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .chat-input {
            flex: 1;
            padding: 0.75rem 1rem;
            border: none;
            border-radius: 25px;
            outline: none;
            font-size: 1rem;
            background: white;
        }

        .send-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #075e54;
            color: white;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.3s;
        }

        .send-btn:hover {
            background: #064e45;
        }

        .send-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .no-chat-selected {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #666;
        }

        .no-chat-selected.hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div id="auth-section">
            <div class="tab-buttons">
                <button class="tab-button active" onclick="showTab('login')">Login</button>
                <button class="tab-button" onclick="showTab('register')">Register</button>
            </div>
            
            <div id="message" class="message"></div>
            
            <div id="login-form" class="form-container active">
                <h2 style="color: #333; margin-bottom: 1.5rem; text-align: center;">Login</h2>
                <form onsubmit="handleLogin(event)">
                    <div class="form-group">
                        <label for="login-username">Username</label>
                        <input type="text" id="login-username" required>
                    </div>
                    <div class="form-group">
                        <label for="login-password">Password</label>
                        <input type="password" id="login-password" required>
                    </div>
                    <button type="submit" class="btn">Login</button>
                </form>
            </div>
            
            <div id="register-form" class="form-container">
                <h2 style="color: #333; margin-bottom: 1.5rem; text-align: center;">Create Account</h2>
                <form onsubmit="handleRegister(event)">
                    <div class="form-group">
                        <label for="register-username">Username</label>
                        <input type="text" id="register-username" required minlength="4">
                    </div>
                    <div class="form-group">
                        <label for="register-password">Password</label>
                        <input type="password" id="register-password" required minlength="6">
                    </div>
                    <div class="form-group">
                        <label for="register-password2">Confirm Password</label>
                        <input type="password" id="register-password2" required>
                    </div>
                    <button type="submit" class="btn">Create Account</button>
                </form>
            </div>
        </div>
        
        <div id="dashboard" class="dashboard">
            <h2 style="color: #333; margin-bottom: 1rem;">Welcome to your Dashboard!</h2>
            <p style="color: #666; margin-bottom: 2rem;">Hello, <strong id="dashboard-username"></strong>! You are successfully logged in.</p>

            <div class="user-info">
                <h3 style="color: #333; margin-bottom: 1rem;">Account Information</h3>
                <p style="color: #666;"><strong>Username:</strong> <span id="user-info-username"></span></p>
                <p style="color: #666;"><strong>Status:</strong> Logged In</p>
            </div>

            <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                <button onclick="openChat()" class="btn" style="width: auto; padding: 0.75rem 2rem;">💬 Open Chat</button>
                <button onclick="logout()" class="btn" style="width: auto; padding: 0.75rem 2rem; background: #dc3545;">Logout</button>
            </div>
        </div>
    </div>

    <!-- WhatsApp-like Chat Interface -->
    <div id="chat-container" class="chat-container">
        <div class="chat-sidebar">
            <div class="chat-header">
                <h3>💬 Chat</h3>
                <button class="back-btn" onclick="closeChat()">✕</button>
            </div>
            <div class="online-users" id="online-users">
                <!-- Online users will be populated here -->
            </div>
        </div>

        <div class="chat-main">
            <div id="no-chat-selected" class="no-chat-selected">
                <h3>Select a user to start chatting</h3>
                <p>Choose someone from the list to begin your conversation</p>
            </div>

            <div id="chat-area" style="display: none; height: 100%; flex-direction: column;">
                <div class="chat-main-header">
                    <div class="user-avatar" id="chat-user-avatar">U</div>
                    <div>
                        <h4 id="chat-user-name">User</h4>
                        <p style="margin: 0; font-size: 0.9rem; opacity: 0.8;">Online</p>
                    </div>
                </div>

                <div class="chat-messages" id="chat-messages">
                    <!-- Messages will be populated here -->
                </div>

                <div class="chat-input-container">
                    <input type="text" class="chat-input" id="message-input" placeholder="Type a message..." onkeypress="handleMessageKeyPress(event)">
                    <button class="send-btn" onclick="sendMessage()">➤</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Simple in-memory storage for demo purposes
        let users = JSON.parse(localStorage.getItem('demoUsers') || '{}');
        let currentUser = localStorage.getItem('currentUser');
        let messages = JSON.parse(localStorage.getItem('chatMessages') || '{}');
        let onlineUsers = JSON.parse(localStorage.getItem('onlineUsers') || '[]');
        let selectedChatUser = null;

        // Check if user is already logged in
        if (currentUser) {
            showDashboard(currentUser);
            updateOnlineStatus();
        }
        
        function showTab(tab) {
            // Update tab buttons
            document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // Update form containers
            document.querySelectorAll('.form-container').forEach(container => container.classList.remove('active'));
            document.getElementById(tab + '-form').classList.add('active');
            
            // Clear message
            hideMessage();
        }
        
        function showMessage(text, type) {
            const messageEl = document.getElementById('message');
            messageEl.textContent = text;
            messageEl.className = `message ${type}`;
            messageEl.style.display = 'block';
        }
        
        function hideMessage() {
            document.getElementById('message').style.display = 'none';
        }
        
        function handleRegister(event) {
            event.preventDefault();
            
            const username = document.getElementById('register-username').value;
            const password = document.getElementById('register-password').value;
            const password2 = document.getElementById('register-password2').value;
            
            if (password !== password2) {
                showMessage('Passwords do not match', 'error');
                return;
            }
            
            if (users[username]) {
                showMessage('Username already exists', 'error');
                return;
            }
            
            users[username] = { password: password };
            localStorage.setItem('demoUsers', JSON.stringify(users));
            
            showMessage('Account created successfully! Please login.', 'success');
            
            // Clear form and switch to login
            document.getElementById('register-form').reset();
            setTimeout(() => {
                document.querySelector('.tab-button').click();
            }, 1500);
        }
        
        function handleLogin(event) {
            event.preventDefault();
            
            const username = document.getElementById('login-username').value;
            const password = document.getElementById('login-password').value;
            
            if (!users[username] || users[username].password !== password) {
                showMessage('Invalid username or password', 'error');
                return;
            }
            
            localStorage.setItem('currentUser', username);
            showDashboard(username);
        }
        
        function showDashboard(username) {
            document.getElementById('auth-section').style.display = 'none';
            document.getElementById('dashboard').classList.add('active');
            document.getElementById('dashboard-username').textContent = username;
            document.getElementById('user-info-username').textContent = username;
        }
        
        function logout() {
            // Remove from online users
            removeFromOnlineUsers(currentUser);
            localStorage.removeItem('currentUser');
            document.getElementById('auth-section').style.display = 'block';
            document.getElementById('dashboard').classList.remove('active');
            document.getElementById('chat-container').classList.remove('active');
            document.getElementById('login-form').reset();
            showMessage('You have been logged out', 'success');
        }

        // Chat Functions
        function updateOnlineStatus() {
            if (currentUser && !onlineUsers.includes(currentUser)) {
                onlineUsers.push(currentUser);
                localStorage.setItem('onlineUsers', JSON.stringify(onlineUsers));
            }
        }

        function removeFromOnlineUsers(username) {
            onlineUsers = onlineUsers.filter(user => user !== username);
            localStorage.setItem('onlineUsers', JSON.stringify(onlineUsers));
        }

        function openChat() {
            document.getElementById('chat-container').classList.add('active');
            loadOnlineUsers();
        }

        function closeChat() {
            document.getElementById('chat-container').classList.remove('active');
            selectedChatUser = null;
            document.getElementById('chat-area').style.display = 'none';
            document.getElementById('no-chat-selected').classList.remove('hidden');
        }

        function loadOnlineUsers() {
            const onlineUsersContainer = document.getElementById('online-users');
            onlineUsersContainer.innerHTML = '';

            // Get all registered users (simulating online users)
            const allUsers = Object.keys(users).filter(user => user !== currentUser);

            if (allUsers.length === 0) {
                onlineUsersContainer.innerHTML = '<div style="padding: 2rem; text-align: center; color: #666;">No other users available.<br>Register more accounts to chat!</div>';
                return;
            }

            allUsers.forEach(username => {
                const userItem = document.createElement('div');
                userItem.className = 'user-item';
                userItem.onclick = () => selectUser(username);

                userItem.innerHTML = `
                    <div class="user-avatar">${username.charAt(0).toUpperCase()}</div>
                    <div class="user-details">
                        <h4>${username}</h4>
                        <p>Online</p>
                    </div>
                `;

                onlineUsersContainer.appendChild(userItem);
            });
        }

        function selectUser(username) {
            selectedChatUser = username;

            // Update UI
            document.querySelectorAll('.user-item').forEach(item => item.classList.remove('active'));
            event.currentTarget.classList.add('active');

            document.getElementById('no-chat-selected').style.display = 'none';
            document.getElementById('chat-area').style.display = 'flex';

            // Update chat header
            document.getElementById('chat-user-name').textContent = username;
            document.getElementById('chat-user-avatar').textContent = username.charAt(0).toUpperCase();

            // Load messages
            loadMessages(username);
        }

        function loadMessages(username) {
            const chatKey = getChatKey(currentUser, username);
            const chatMessages = messages[chatKey] || [];
            const messagesContainer = document.getElementById('chat-messages');

            messagesContainer.innerHTML = '';

            if (chatMessages.length === 0) {
                messagesContainer.innerHTML = '<div style="text-align: center; color: #666; padding: 2rem;">No messages yet. Start the conversation!</div>';
                return;
            }

            chatMessages.forEach(msg => {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${msg.sender === currentUser ? 'own' : ''}`;

                const time = new Date(msg.timestamp).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

                messageDiv.innerHTML = `
                    <div class="message-bubble">
                        ${msg.text}
                        <div class="message-info">${time}</div>
                    </div>
                `;

                messagesContainer.appendChild(messageDiv);
            });

            // Scroll to bottom
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function getChatKey(user1, user2) {
            return [user1, user2].sort().join('_');
        }

        function sendMessage() {
            const input = document.getElementById('message-input');
            const messageText = input.value.trim();

            if (!messageText || !selectedChatUser) return;

            const chatKey = getChatKey(currentUser, selectedChatUser);
            if (!messages[chatKey]) {
                messages[chatKey] = [];
            }

            const message = {
                sender: currentUser,
                text: messageText,
                timestamp: new Date().toISOString()
            };

            messages[chatKey].push(message);
            localStorage.setItem('chatMessages', JSON.stringify(messages));

            // Clear input
            input.value = '';

            // Reload messages
            loadMessages(selectedChatUser);
        }

        function handleMessageKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }
    </script>
</body>
</html>
