{% extends "base.html" %}

{% block title %}Register - User Authentication App{% endblock %}

{% block content %}
<h2 style="color: #333; margin-bottom: 1.5rem; text-align: center;">Create Account</h2>

<form method="POST">
    {{ form.hidden_tag() }}
    
    <div class="form-group">
        {{ form.username.label(class="form-label") }}
        {{ form.username(class="form-control", placeholder="Choose a username") }}
        {% if form.username.errors %}
            <div style="color: #dc3545; font-size: 0.875rem; margin-top: 0.25rem;">
                {% for error in form.username.errors %}
                    <div>{{ error }}</div>
                {% endfor %}
            </div>
        {% endif %}
    </div>

    <div class="form-group">
        {{ form.email.label(class="form-label") }}
        {{ form.email(class="form-control", placeholder="Enter your email address") }}
        {% if form.email.errors %}
            <div style="color: #dc3545; font-size: 0.875rem; margin-top: 0.25rem;">
                {% for error in form.email.errors %}
                    <div>{{ error }}</div>
                {% endfor %}
            </div>
        {% endif %}
    </div>
    
    <div class="form-group">
        {{ form.password.label(class="form-label") }}
        {{ form.password(class="form-control") }}
        {% if form.password.errors %}
            <div style="color: #dc3545; font-size: 0.875rem; margin-top: 0.25rem;">
                {% for error in form.password.errors %}
                    <div>{{ error }}</div>
                {% endfor %}
            </div>
        {% endif %}
    </div>
    
    <div class="form-group">
        {{ form.password2.label(class="form-label") }}
        {{ form.password2(class="form-control") }}
        {% if form.password2.errors %}
            <div style="color: #dc3545; font-size: 0.875rem; margin-top: 0.25rem;">
                {% for error in form.password2.errors %}
                    <div>{{ error }}</div>
                {% endfor %}
            </div>
        {% endif %}
    </div>
    
    {{ form.submit(class="btn") }}
</form>

<div class="text-center mt-2">
    <p style="color: #666;">Already have an account? <a href="{{ url_for('login') }}" class="link">Login here</a></p>
</div>
{% endblock %}
