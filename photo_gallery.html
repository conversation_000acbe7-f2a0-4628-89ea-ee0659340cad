<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Photo Gallery - How to Add Images</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        /* Hero section with background image */
        .hero {
            height: 60vh;
            background-image: linear-gradient(rgba(102, 126, 234, 0.8), rgba(118, 75, 162, 0.8)),
                              url('https://images.unsplash.com/photo-1519904981063-b0cf448d479e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80');
            background-size: cover;
            background-position: center;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            border-radius: 15px;
            margin: 2rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        
        .hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .hero p {
            font-size: 1.2rem;
            max-width: 600px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin-top: 2rem;
            margin-bottom: 2rem;
        }
        
        .section {
            margin-bottom: 3rem;
        }
        
        .section h2 {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 0.5rem;
        }
        
        /* Image gallery grid */
        .gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }
        
        .gallery-item {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.3s ease;
        }
        
        .gallery-item:hover {
            transform: translateY(-5px);
        }
        
        .gallery-item img {
            width: 100%;
            height: 250px;
            object-fit: cover;
        }
        
        .gallery-item-content {
            padding: 1.5rem;
        }
        
        .gallery-item h3 {
            margin-bottom: 0.5rem;
            color: #2c3e50;
        }
        
        /* Responsive images */
        .responsive-demo img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            margin: 1rem 0;
        }
        
        /* Image with different shapes */
        .image-shapes {
            display: flex;
            gap: 2rem;
            flex-wrap: wrap;
            margin: 2rem 0;
        }
        
        .shape-demo {
            text-align: center;
        }
        
        .shape-demo img {
            width: 150px;
            height: 150px;
            object-fit: cover;
        }
        
        .circle {
            border-radius: 50%;
        }
        
        .rounded {
            border-radius: 15px;
        }
        
        .border {
            border: 5px solid #3498db;
        }
        
        /* Code examples */
        .code-example {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }
        
        .code-example pre {
            margin: 0;
            white-space: pre-wrap;
        }
        
        /* Image overlay effects */
        .overlay-container {
            position: relative;
            display: inline-block;
            margin: 1rem;
        }
        
        .overlay-container img {
            width: 300px;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
        }
        
        .overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(52, 152, 219, 0.8);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
            border-radius: 8px;
        }
        
        .overlay-container:hover .overlay {
            opacity: 1;
        }
        
        /* Lazy loading demo */
        .lazy-image {
            background: #f0f0f0;
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            margin: 1rem 0;
        }
        
        .btn {
            background: #3498db;
            color: white;
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            transition: background 0.3s ease;
        }
        
        .btn:hover {
            background: #2980b9;
        }
        
        .upload-area {
            border: 2px dashed #3498db;
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            margin: 1rem 0;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .upload-area:hover {
            background: #f8f9fa;
        }
        
        .upload-area.dragover {
            background: #e3f2fd;
            border-color: #2196f3;
        }
        
        #preview-container {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .preview-item {
            position: relative;
            display: inline-block;
        }
        
        .preview-item img {
            width: 150px;
            height: 150px;
            object-fit: cover;
            border-radius: 8px;
        }
        
        .remove-btn {
            position: absolute;
            top: 5px;
            right: 5px;
            background: #e74c3c;
            color: white;
            border: none;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            cursor: pointer;
            font-size: 12px;
        }

        .profile-header {
            background: #f8f9fa;
            padding: 1.5rem 0;
            text-align: center;
            border-bottom: 1px solid #e9ecef;
        }

        .profile-info h2 {
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .profile-info p {
            color: #7f8c8d;
            font-size: 1.1rem;
        }
    </style>
</head>
<body>
    <!-- Hero Section with Background Image -->
    <section class="hero">
        <div>
            <h1>🖼️ Adding Images to Websites</h1>
            <p>Learn different methods to add and style images in your web projects</p>
        </div>
    </section>

    <div class="profile-header">
        <div class="profile-info">
            <h2>John Doe</h2>
            <p>Photography Enthusiast</p>
        </div>
    </div>

    <div class="container">
        <!-- Method 1: Basic HTML Images -->
        <section class="section">
            <h2>1. Basic HTML Images</h2>
            <p>The most common way to add images using the &lt;img&gt; tag:</p>
            
            <div class="code-example">
                <pre>&lt;img src="image.jpg" alt="Description of image"&gt;</pre>
            </div>
            
            <div class="responsive-demo">
                <img src="https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Forest landscape">
                <p><em>Example: Responsive image that scales with container</em></p>
            </div>
        </section>
        
        <!-- Method 2: Image Gallery -->
        <section class="section">
            <h2>2. Image Gallery</h2>
            <p>Creating a responsive image gallery:</p>
            
            <div class="gallery">
                <div class="gallery-item">
                    <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Mountain landscape">
                    <div class="gallery-item-content">
                        <h3>Mountain View</h3>
                        <p>Beautiful mountain landscape with clear skies</p>
                    </div>
                </div>
                
                <div class="gallery-item">
                    <img src="https://images.unsplash.com/photo-1518837695005-2083093ee35b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Ocean waves">
                    <div class="gallery-item-content">
                        <h3>Ocean Waves</h3>
                        <p>Peaceful ocean waves on a sunny day</p>
                    </div>
                </div>
                
                <div class="gallery-item">
                    <img src="https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Forest path">
                    <div class="gallery-item-content">
                        <h3>Forest Path</h3>
                        <p>A winding path through a dense forest</p>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Method 3: Different Image Shapes -->
        <section class="section">
            <h2>3. Image Styling & Shapes</h2>
            <p>Different ways to style your images:</p>
            
            <div class="image-shapes">
                <div class="shape-demo">
                    <img src="https://images.unsplash.com/photo-1494790108755-2616c9c9b8d4?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80" alt="Portrait" class="circle">
                    <p>Circle</p>
                </div>
                
                <div class="shape-demo">
                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80" alt="Portrait" class="rounded">
                    <p>Rounded</p>
                </div>
                
                <div class="shape-demo">
                    <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80" alt="Portrait" class="border">
                    <p>With Border</p>
                </div>
            </div>
            
            <div class="code-example">
                <pre>/* Circle image */
.circle {
    border-radius: 50%;
}

/* Rounded corners */
.rounded {
    border-radius: 15px;
}

/* With border */
.border {
    border: 5px solid #3498db;
}</pre>
            </div>
        </section>
        
        <!-- Method 4: Image Overlays -->
        <section class="section">
            <h2>4. Image Overlays & Effects</h2>
            <p>Adding hover effects and overlays:</p>
            
            <div class="overlay-container">
                <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80" alt="Mountain">
                <div class="overlay">
                    <span>Hover Effect!</span>
                </div>
            </div>
            
            <div class="overlay-container">
                <img src="https://images.unsplash.com/photo-1518837695005-2083093ee35b?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80" alt="Ocean">
                <div class="overlay">
                    <span>Click to View</span>
                </div>
            </div>
        </section>
        
        <!-- Method 5: Background to Foreground -->
        <section class="section">
            <h2>5. Bringing Background Photos to Front</h2>
            <p>Different methods to convert background images to foreground images:</p>

            <!-- Method 5a: Extract Background as Image -->
            <h3>Method 1: Extract Background Image</h3>
            <div style="margin: 2rem 0;">
                <p>Current hero background image brought to front:</p>
                <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                     alt="Mountain landscape - extracted from background"
                     style="width: 100%; max-width: 600px; border-radius: 10px; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
            </div>

            <!-- Method 5b: Background with Overlay Button -->
            <h3>Method 2: Background with "Bring to Front" Button</h3>
            <div style="position: relative; height: 300px; background-image: url('https://images.unsplash.com/photo-1518837695005-2083093ee35b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'); background-size: cover; background-position: center; border-radius: 10px; margin: 2rem 0;">
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
                    <button onclick="bringBackgroundToFront(this)" style="background: rgba(255,255,255,0.9); border: none; padding: 1rem 2rem; border-radius: 25px; cursor: pointer; font-weight: bold; box-shadow: 0 5px 15px rgba(0,0,0,0.3);">
                        📸 Bring to Front
                    </button>
                </div>
            </div>
            <div id="extracted-image-container" style="margin-top: 1rem;"></div>

            <!-- Method 5c: CSS Transform Method -->
            <h3>Method 3: CSS Transform Method</h3>
            <div class="background-to-front-demo" style="margin: 2rem 0;">
                <div class="bg-container" style="position: relative; height: 250px; background-image: url('https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'); background-size: cover; background-position: center; border-radius: 10px; overflow: hidden; cursor: pointer;" onclick="transformBackground(this)">
                    <div style="position: absolute; bottom: 20px; left: 20px; background: rgba(0,0,0,0.7); color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem;">
                        Click to transform →
                    </div>
                </div>
            </div>

            <!-- Method 5d: Multiple Background Layers -->
            <h3>Method 4: Layer Management</h3>
            <div class="layer-demo" style="position: relative; height: 300px; margin: 2rem 0;">
                <!-- Background Layer -->
                <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-image: url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'); background-size: cover; background-position: center; border-radius: 10px; z-index: 1;"></div>

                <!-- Foreground Controls -->
                <div style="position: absolute; top: 20px; right: 20px; z-index: 3;">
                    <button onclick="toggleLayer('bg-layer')" style="background: rgba(255,255,255,0.9); border: none; padding: 0.5rem 1rem; border-radius: 20px; margin: 0.2rem; cursor: pointer; font-size: 0.8rem;">Toggle BG</button>
                    <button onclick="bringLayerToFront()" style="background: rgba(52,152,219,0.9); color: white; border: none; padding: 0.5rem 1rem; border-radius: 20px; margin: 0.2rem; cursor: pointer; font-size: 0.8rem;">Bring Front</button>
                </div>

                <!-- Extracted Image (initially hidden) -->
                <img id="extracted-layer-image" src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"
                     alt="Extracted image"
                     style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%) scale(0); width: 80%; height: 80%; object-fit: cover; border-radius: 10px; box-shadow: 0 20px 40px rgba(0,0,0,0.3); z-index: 2; transition: transform 0.5s ease;">
            </div>
        </section>

        <!-- Method 6: Upload Images -->
        <section class="section">
            <h2>6. Upload Your Own Images</h2>
            <p>Try uploading your own images:</p>
            
            <div class="upload-area" onclick="document.getElementById('file-input').click()">
                <p>📁 Click here or drag & drop images</p>
                <p style="font-size: 0.9rem; color: #666;">Supports: JPG, PNG, GIF</p>
            </div>
            
            <input type="file" id="file-input" multiple accept="image/*" style="display: none;">
            
            <div id="preview-container"></div>
        </section>
        
        <!-- Code Examples -->
        <section class="section">
            <h2>6. Code Examples</h2>
            
            <h3>HTML Examples:</h3>
            <div class="code-example">
                <pre>&lt;!-- Basic image --&gt;
&lt;img src="photo.jpg" alt="My photo"&gt;

&lt;!-- Responsive image --&gt;
&lt;img src="photo.jpg" alt="My photo" style="max-width: 100%; height: auto;"&gt;

&lt;!-- Image with specific size --&gt;
&lt;img src="photo.jpg" alt="My photo" width="300" height="200"&gt;

&lt;!-- Online image --&gt;
&lt;img src="https://example.com/image.jpg" alt="Online image"&gt;</pre>
            </div>
            
            <h3>CSS Background Examples:</h3>
            <div class="code-example">
                <pre>/* Background image */
.hero {
    background-image: url('background.jpg');
    background-size: cover;
    background-position: center;
    height: 400px;
}

/* Responsive background */
.section {
    background-image: url('image.jpg');
    background-size: contain;
    background-repeat: no-repeat;
}</pre>
            </div>
        </section>
    </div>
    
    <script>
        // File upload functionality
        const fileInput = document.getElementById('file-input');
        const previewContainer = document.getElementById('preview-container');
        const uploadArea = document.querySelector('.upload-area');
        
        fileInput.addEventListener('change', handleFiles);
        
        // Drag and drop functionality
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            handleFiles({ target: { files } });
        });
        
        function handleFiles(event) {
            const files = event.target.files;
            
            for (let file of files) {
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    
                    reader.onload = function(e) {
                        const previewItem = document.createElement('div');
                        previewItem.className = 'preview-item';
                        
                        previewItem.innerHTML = `
                            <img src="${e.target.result}" alt="Uploaded image">
                            <button class="remove-btn" onclick="this.parentElement.remove()">×</button>
                        `;
                        
                        previewContainer.appendChild(previewItem);
                    };
                    
                    reader.readAsDataURL(file);
                }
            }
        }
        
        // Image loading demo
        function loadImage() {
            const lazyImage = document.querySelector('.lazy-image');
            lazyImage.innerHTML = '<img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80" alt="Loaded image" style="max-width: 100%; border-radius: 8px;">';
        }

        // Background to Front Functions
        function bringBackgroundToFront(button) {
            const container = button.closest('div[style*="background-image"]');
            const backgroundImage = container.style.backgroundImage;

            // Extract URL from background-image CSS
            const urlMatch = backgroundImage.match(/url\(['"]?(.*?)['"]?\)/);
            if (urlMatch) {
                const imageUrl = urlMatch[1];

                // Create new image element
                const img = document.createElement('img');
                img.src = imageUrl;
                img.alt = 'Extracted background image';
                img.style.cssText = `
                    width: 100%;
                    max-width: 600px;
                    border-radius: 10px;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                    animation: slideIn 0.5s ease-out;
                `;

                // Add to container
                const extractedContainer = document.getElementById('extracted-image-container');
                extractedContainer.innerHTML = '<p><strong>✅ Background image brought to front:</strong></p>';
                extractedContainer.appendChild(img);

                // Hide the button
                button.style.display = 'none';

                // Add reset button
                const resetBtn = document.createElement('button');
                resetBtn.textContent = '🔄 Reset';
                resetBtn.style.cssText = 'background: #e74c3c; color: white; border: none; padding: 0.5rem 1rem; border-radius: 20px; margin-left: 1rem; cursor: pointer;';
                resetBtn.onclick = () => {
                    extractedContainer.innerHTML = '';
                    button.style.display = 'inline-block';
                };
                extractedContainer.appendChild(resetBtn);
            }
        }

        function transformBackground(element) {
            const backgroundImage = element.style.backgroundImage;
            const urlMatch = backgroundImage.match(/url\(['"]?(.*?)['"]?\)/);

            if (urlMatch) {
                const imageUrl = urlMatch[1];

                // Create transformation effect
                element.style.transform = 'scale(0.8)';
                element.style.transition = 'transform 0.5s ease';

                setTimeout(() => {
                    // Replace with image element
                    const img = document.createElement('img');
                    img.src = imageUrl;
                    img.alt = 'Transformed image';
                    img.style.cssText = `
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                        border-radius: 10px;
                        box-shadow: 0 15px 35px rgba(0,0,0,0.3);
                        animation: popIn 0.5s ease-out;
                    `;

                    element.innerHTML = '';
                    element.appendChild(img);
                    element.style.background = 'none';
                    element.style.transform = 'scale(1)';

                    // Add click to reset
                    element.onclick = () => location.reload();
                }, 250);
            }
        }

        function toggleLayer(layerId) {
            const layers = document.querySelectorAll('.layer-demo > div');
            layers[0].style.display = layers[0].style.display === 'none' ? 'block' : 'none';
        }

        function bringLayerToFront() {
            const extractedImage = document.getElementById('extracted-layer-image');
            extractedImage.style.transform = 'translate(-50%, -50%) scale(1)';

            // Add click to reset
            extractedImage.onclick = () => {
                extractedImage.style.transform = 'translate(-50%, -50%) scale(0)';
                extractedImage.onclick = null;
            };
        }

        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { opacity: 0; transform: translateY(20px); }
                to { opacity: 1; transform: translateY(0); }
            }

            @keyframes popIn {
                from { opacity: 0; transform: scale(0.8); }
                to { opacity: 1; transform: scale(1); }
            }

            .bg-container:hover {
                transform: scale(1.02);
                transition: transform 0.3s ease;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>





