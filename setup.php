<?php
/**
 * Smart Healthcare Management System - Setup Page
 * 
 * This page helps you set up the database connection and import the SQL file
 */

require_once 'config/database.php';

// Handle database import
if (isset($_POST['import_database'])) {
    try {
        $db = new Database();
        $conn = $db->connect();
        
        // Read SQL file
        $sqlFile = 'smart_database.sql';
        if (file_exists($sqlFile)) {
            $sql = file_get_contents($sqlFile);
            
            // Execute SQL commands
            $conn->exec($sql);
            
            $success_message = "Database imported successfully!";
        } else {
            $error_message = "SQL file not found!";
        }
        
    } catch (Exception $e) {
        $error_message = "Error importing database: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Healthcare - Database Setup</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .content {
            padding: 2rem;
        }
        
        .step {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 1.5rem;
            margin: 1rem 0;
            border-radius: 0 8px 8px 0;
        }
        
        .step h3 {
            color: #333;
            margin-bottom: 1rem;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
        
        .code {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            margin: 1rem 0;
        }
        
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 Smart Healthcare Management System</h1>
            <p>Database Setup & Connection</p>
        </div>
        
        <div class="content">
            <?php if (isset($success_message)): ?>
                <div class="success">✅ <?php echo $success_message; ?></div>
            <?php endif; ?>
            
            <?php if (isset($error_message)): ?>
                <div class="error">❌ <?php echo $error_message; ?></div>
            <?php endif; ?>
            
            <div class="step">
                <h3>Step 1: Start XAMPP Services</h3>
                <p>Make sure you have XAMPP installed and running:</p>
                <ul style="margin: 1rem 0; padding-left: 2rem;">
                    <li>Open XAMPP Control Panel</li>
                    <li>Start <strong>Apache</strong> service</li>
                    <li>Start <strong>MySQL</strong> service</li>
                </ul>
                <div class="warning">
                    ⚠️ Both Apache and MySQL must be running for this setup to work!
                </div>
            </div>
            
            <div class="step">
                <h3>Step 2: Test Database Connection</h3>
                <p>Click the button below to test if the database connection is working:</p>
                <br>
                <button class="btn" onclick="testConnection()">🔍 Test Connection</button>
                <div id="connection-result" style="margin-top: 1rem;"></div>
            </div>
            
            <div class="step">
                <h3>Step 3: Import Database</h3>
                <p>Import the Smart Healthcare database structure:</p>
                <br>
                <form method="POST">
                    <button type="submit" name="import_database" class="btn">📥 Import Database</button>
                </form>
            </div>
            
            <div class="step">
                <h3>Step 4: Project Structure</h3>
                <p>Your project should be placed in the XAMPP htdocs folder:</p>
                <div class="code">
                    C:\xampp\htdocs\your-project-name\
                </div>
                <p>Then access it via: <code>http://localhost/your-project-name/</code></p>
            </div>
            
            <div class="step">
                <h3>Step 5: Database Tables</h3>
                <p>Your Smart Healthcare system includes these tables:</p>
                <ul style="margin: 1rem 0; padding-left: 2rem;">
                    <li><strong>clinics</strong> - Clinic information</li>
                    <li><strong>hospitals</strong> - Hospital data</li>
                    <li><strong>messages</strong> - System messages</li>
                    <li><strong>network_login</strong> - Login tracking</li>
                    <li><strong>patient_records</strong> - Patient information</li>
                    <li><strong>user</strong> - User accounts</li>
                </ul>
            </div>
            
            <div class="step">
                <h3>Next Steps</h3>
                <p>After successful setup:</p>
                <ul style="margin: 1rem 0; padding-left: 2rem;">
                    <li>Create your main application files (index.php, login.php, etc.)</li>
                    <li>Use the database connection from <code>config/database.php</code></li>
                    <li>Build your healthcare management features</li>
                </ul>
            </div>
        </div>
    </div>
    
    <script>
        function testConnection() {
            const resultDiv = document.getElementById('connection-result');
            resultDiv.innerHTML = '<p>Testing connection...</p>';
            
            // Make AJAX request to test connection
            fetch('test_connection.php')
                .then(response => response.text())
                .then(data => {
                    resultDiv.innerHTML = data;
                })
                .catch(error => {
                    resultDiv.innerHTML = '<div class="error">Error testing connection: ' + error + '</div>';
                });
        }
    </script>
</body>
</html>
