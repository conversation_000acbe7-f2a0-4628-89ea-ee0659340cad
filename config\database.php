<?php
/**
 * Database Configuration for Smart Healthcare Management System
 * 
 * This file contains database connection settings for XAMPP/MySQL
 * Make sure XAMPP is running with Apache and MySQL services started
 */

// Database configuration constants
define('DB_HOST', 'localhost');        // Database host (usually localhost for XAMPP)
define('DB_USERNAME', 'root');         // Database username (default 'root' for XAMPP)
define('DB_PASSWORD', '');             // Database password (empty by default for XAMPP)
define('DB_NAME', 'smart');            // Database name

// Database connection class
class Database {
    private $host = DB_HOST;
    private $username = DB_USERNAME;
    private $password = DB_PASSWORD;
    private $database = DB_NAME;
    private $connection;
    
    /**
     * Create database connection
     */
    public function connect() {
        $this->connection = null;
        
        try {
            // Create PDO connection
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->database . ";charset=utf8mb4";
            $this->connection = new PDO($dsn, $this->username, $this->password);
            
            // Set PDO attributes
            $this->connection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->connection->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
            
            echo "<!-- Database connected successfully -->\n";
            
        } catch(PDOException $e) {
            echo "Connection Error: " . $e->getMessage();
            die();
        }
        
        return $this->connection;
    }
    
    /**
     * Close database connection
     */
    public function disconnect() {
        $this->connection = null;
    }
    
    /**
     * Get connection instance
     */
    public function getConnection() {
        return $this->connection;
    }
}

// Alternative MySQLi connection function
function connectMySQLi() {
    $connection = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_NAME);
    
    // Check connection
    if ($connection->connect_error) {
        die("Connection failed: " . $connection->connect_error);
    }
    
    // Set charset
    $connection->set_charset("utf8mb4");
    
    return $connection;
}

// Simple connection test function
function testConnection() {
    try {
        $db = new Database();
        $conn = $db->connect();
        
        if ($conn) {
            echo "<div style='color: green; padding: 10px; border: 1px solid green; margin: 10px; border-radius: 5px;'>";
            echo "✅ Database connection successful!<br>";
            echo "Host: " . DB_HOST . "<br>";
            echo "Database: " . DB_NAME . "<br>";
            echo "Status: Connected";
            echo "</div>";
            
            // Test query to check if tables exist
            $stmt = $conn->prepare("SHOW TABLES");
            $stmt->execute();
            $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            if (!empty($tables)) {
                echo "<div style='color: blue; padding: 10px; border: 1px solid blue; margin: 10px; border-radius: 5px;'>";
                echo "📋 Available tables:<br>";
                foreach ($tables as $table) {
                    echo "• " . $table . "<br>";
                }
                echo "</div>";
            }
            
        }
    } catch (Exception $e) {
        echo "<div style='color: red; padding: 10px; border: 1px solid red; margin: 10px; border-radius: 5px;'>";
        echo "❌ Database connection failed!<br>";
        echo "Error: " . $e->getMessage() . "<br>";
        echo "Please check if XAMPP is running and MySQL service is started.";
        echo "</div>";
    }
}

?>
