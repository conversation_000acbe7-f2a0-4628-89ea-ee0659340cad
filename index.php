<?php
/**
 * Smart Healthcare Management System - Main Page
 */

require_once 'config/database.php';

// Initialize database connection
try {
    $db = new Database();
    $conn = $db->connect();
    $connection_status = "Connected";
} catch (Exception $e) {
    $connection_status = "Connection Error: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Healthcare Management System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .nav {
            background: #f8f9fa;
            padding: 1rem 2rem;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .nav ul {
            list-style: none;
            display: flex;
            gap: 2rem;
        }
        
        .nav a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .nav a:hover {
            background: #e9ecef;
        }
        
        .content {
            padding: 2rem;
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .card {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        
        .card h3 {
            color: #333;
            margin-bottom: 1rem;
        }
        
        .card p {
            color: #666;
            margin-bottom: 1.5rem;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-weight: 600;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .status {
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 Smart Healthcare Management System</h1>
            <p>Comprehensive Healthcare Data Management Platform</p>
        </div>
        
        <nav class="nav">
            <ul>
                <li><a href="index.php">🏠 Dashboard</a></li>
                <li><a href="patients.php">👥 Patients</a></li>
                <li><a href="clinics.php">🏥 Clinics</a></li>
                <li><a href="hospitals.php">🏨 Hospitals</a></li>
                <li><a href="users.php">👤 Users</a></li>
                <li><a href="setup.php">⚙️ Setup</a></li>
            </ul>
        </nav>
        
        <div class="content">
            <div class="status <?php echo (strpos($connection_status, 'Error') === false) ? 'success' : 'error'; ?>">
                <strong>Database Status:</strong> <?php echo $connection_status; ?>
            </div>
            
            <h2>Welcome to Smart Healthcare Management</h2>
            <p>This system helps you manage healthcare data across multiple clinics and hospitals.</p>
            
            <div class="dashboard">
                <div class="card">
                    <h3>👥 Patient Records</h3>
                    <p>Manage patient information, diagnoses, and medical history</p>
                    <a href="patients.php" class="btn">View Patients</a>
                </div>
                
                <div class="card">
                    <h3>🏥 Clinics</h3>
                    <p>Manage clinic information and locations</p>
                    <a href="clinics.php" class="btn">View Clinics</a>
                </div>
                
                <div class="card">
                    <h3>🏨 Hospitals</h3>
                    <p>Hospital management and contact information</p>
                    <a href="hospitals.php" class="btn">View Hospitals</a>
                </div>
                
                <div class="card">
                    <h3>👤 User Management</h3>
                    <p>Manage system users and access permissions</p>
                    <a href="users.php" class="btn">View Users</a>
                </div>
                
                <div class="card">
                    <h3>💬 Messages</h3>
                    <p>Internal messaging system</p>
                    <a href="messages.php" class="btn">View Messages</a>
                </div>
                
                <div class="card">
                    <h3>📊 Reports</h3>
                    <p>Generate reports and analytics</p>
                    <a href="reports.php" class="btn">View Reports</a>
                </div>
            </div>
            
            <div style="margin-top: 3rem; padding: 2rem; background: #f8f9fa; border-radius: 10px;">
                <h3>Quick Start Guide</h3>
                <ol style="margin: 1rem 0; padding-left: 2rem;">
                    <li>Make sure XAMPP is running (Apache + MySQL)</li>
                    <li>Go to <a href="setup.php" style="color: #667eea;">Setup Page</a> to import the database</li>
                    <li>Start adding clinics, hospitals, and users</li>
                    <li>Begin managing patient records</li>
                </ol>
            </div>
        </div>
    </div>
</body>
</html>
